/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInDown 1s ease-out;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 3rem;
    color: #fff;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    animation: rotate3d 3s ease-in-out infinite;
}

.logo h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    font-weight: 300;
}

/* Step Section Styles */
.step-section {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transform: translateY(20px);
    opacity: 0;
    animation: slideInUp 0.8s ease-out forwards;
    position: relative;
    overflow: hidden;
}

.step-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.step-section:hover::before {
    left: 100%;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    animation: pulse3d 2s ease-in-out infinite;
}

.step-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
}

/* Upload Area Styles */
.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: #764ba2;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.upload-area.dragover {
    border-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 25px;
}

/* Button Styles */
.btn, .upload-btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    box-shadow: 0 8px 16px rgba(108, 117, 125, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3);
}

.btn:hover, .upload-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.2);
}

.btn:active, .upload-btn:active {
    transform: translateY(-1px);
}

/* Crop Container Styles */
.crop-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

.crop-area {
    width: 400px;
    height: 400px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
    background: #f8f9fa;
}

/* Croppie Custom Styles */
.croppie-container {
    border-radius: 15px !important;
}

.croppie-container .cr-boundary {
    border-radius: 15px !important;
}

.croppie-container .cr-viewport {
    border: 3px solid #667eea !important;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3) !important;
}

.croppie-container .cr-slider-wrap {
    margin-top: 20px !important;
}

.croppie-container .cr-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 10px !important;
    height: 8px !important;
}

.croppie-container .cr-slider::-webkit-slider-thumb {
    background: #fff !important;
    border: 2px solid #667eea !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
}

.crop-controls {
    display: flex;
    gap: 15px;
}

/* Progress Bar Styles */
.progress-container {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

.progress-text {
    text-align: center;
    font-weight: 500;
    color: #333;
}

/* Preview Container */
.preview-container {
    text-align: center;
    animation: slideInUp 0.6s ease-out;
}

.preview-header {
    margin-bottom: 25px;
}

.preview-header h3 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.preview-header p {
    color: #666;
    font-size: 1.1rem;
}

.preview-image-container {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 30px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow: inset 0 4px 8px rgba(0,0,0,0.1);
}

#final-canvas {
    width: 500px;
    height: 500px;
    max-width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    animation: zoomIn 0.5s ease-out;
    transition: transform 0.3s ease;
    background: #f8f9fa;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

#final-canvas:hover {
    transform: scale(1.02);
}

.preview-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 25px;
    color: #333;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.info-item i {
    color: #667eea;
    font-size: 1.1rem;
}

.download-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 50px;
    padding: 20px;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotate3d {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
}

@keyframes pulse3d {
    0%, 100% { transform: scale(1) rotateZ(0deg); }
    50% { transform: scale(1.1) rotateZ(5deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .step-section {
        padding: 20px;
    }
    
    .crop-area {
        width: 300px;
        height: 300px;
    }
    
    .download-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .preview-info {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .info-item {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .preview-header h3 {
        font-size: 1.5rem;
    }

    #final-canvas {
        width: 100%;
        height: auto;
        max-width: 400px;
    }
}

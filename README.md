# Donance-S Pro Photo Activity

A beautiful photo framing application that allows users to upload photos, crop them in a circular shape, and apply a professional frame.

## 🚀 Quick Start

### Method 1: Using Python Server (Recommended)
1. **Double-click** `start_server.bat` (Windows)
2. **Or run manually**: `python start_server.py`
3. <PERSON><PERSON><PERSON> will open automatically at `http://localhost:8000`

### Method 2: Using Node.js (Alternative)
```bash
npx http-server -p 8000 -c-1
```

### Method 3: Using PHP (Alternative)
```bash
php -S localhost:8000
```

## 📁 Project Structure

```
📁 Donance-S Pro Photo Activity/
├── 📄 index.html              # Main application file
├── 📁 css/
│   └── 📄 style.css           # Styles with 3D animations
├── 📁 js/
│   └── 📄 app.js              # Application logic
├── 📁 poster/
│   └── 🖼️ poster.png          # Frame image
├── 📁 assets/                 # Additional assets
├── 📁 libs/                   # Libraries
├── 🐍 start_server.py         # Python server
├── 📄 start_server.bat        # Windows batch file
└── 📄 README.md               # This file
```

## ✨ Features

- **📸 Photo Upload**: Drag & drop or click to upload
- **✂️ Round Cropping**: Professional circular cropping with CropMe
- **🎨 3D Animations**: Modern UI with beautiful animations
- **📊 Progress Tracking**: Real-time progress indicators
- **🖼️ Frame Application**: Automatic Donance-S Pro frame overlay
- **💾 High Quality Download**: PNG format with timestamp
- **📱 Responsive Design**: Works on desktop and mobile
- **🎯 Preview**: See exactly how your photo will look

## 🔧 Technical Details

### Libraries Used
- **CropMe**: For circular image cropping
- **Font Awesome**: For icons
- **Google Fonts**: Poppins font family

### Browser Support
- Chrome (recommended)
- Firefox
- Safari
- Edge

## 🐛 Troubleshooting

### CORS Error (file:// protocol)
**Problem**: "Access to image blocked by CORS policy"
**Solution**: Use the provided server (`start_server.bat` or `python start_server.py`)

### Image Not Loading
**Problem**: Frame image not appearing
**Solution**: 
1. Check if `poster/poster.png` exists
2. Use local server instead of file:// protocol
3. Check browser console for errors

### Cropping Issues
**Problem**: Image zooms out too much
**Solution**: Minimum zoom is set to 1.0 to prevent over-zooming

## 🎯 Usage Instructions

1. **Upload Photo**: 
   - Drag & drop an image file
   - Or click "Choose Photo" button

2. **Crop Image**:
   - Use mouse to pan the image
   - Scroll to zoom in/out (limited to prevent over-zooming)
   - Click "Apply Crop" when satisfied

3. **Download**:
   - Wait for processing to complete
   - Preview your framed photo
   - Click "Download Framed Photo"

## 🔒 Privacy

- All processing happens locally in your browser
- No images are uploaded to any server
- Your photos remain private and secure

## 📞 Support

For issues or questions, check the browser console for error messages and ensure you're using a local server to avoid CORS issues.

---

**© 2025 Donance-S Pro Photo Activity. All rights reserved.**
